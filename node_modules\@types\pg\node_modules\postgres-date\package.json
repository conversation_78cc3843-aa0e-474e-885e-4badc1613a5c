{"name": "postgres-date", "main": "index.js", "version": "2.1.0", "description": "Postgres date column parser", "license": "MIT", "repository": "bendrucker/postgres-date", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "engines": {"node": ">=12"}, "scripts": {"test": "standard && tape test.js && tsd"}, "keywords": ["postgres", "date", "parser"], "dependencies": {}, "devDependencies": {"standard": "^16.0.0", "tape": "^5.0.0", "tsd": "^0.19.0"}, "files": ["index.js", "index.d.ts", "readme.md"]}