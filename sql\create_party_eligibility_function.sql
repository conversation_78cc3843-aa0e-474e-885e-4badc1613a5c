-- Drop existing function if it exists
DROP FUNCTION IF EXISTS recall_loop.check_party_eligibility(uuid);

-- Create function to check if party_id belongs to caregiver or tenant_admin
-- Returns 'yes' if eligible for email, 'no' if not
CREATE OR REPLACE FUNCTION recall_loop.check_party_eligibility(party_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    caregiver_party_id uuid;
    party_type_key_val text;
    user_email text;
    user_first_name text;
    user_last_name text;
    user_id_val uuid;
    
    exception_message text;
    exception_detail text;
    exception_sqlstate text;
    exception_hint text;
    final_exception_detail text;
    p_party_id uuid := party_id;

BEGIN
    -- Validate input parameter
    IF party_id IS NULL THEN
        RETURN jsonb_build_object(
            'eligible', 'no',
            'reason', 'party_id is required'
        );
    END IF;

    -- Step 1: Get caregiver_party_id from recall_loop.game_assignment where id = party_id
    SELECT ga.caregiver_party_id
    INTO caregiver_party_id
    FROM recall_loop.game_assignment ga
    WHERE ga.id = p_party_id;

    IF caregiver_party_id IS NULL THEN
        RETURN jsonb_build_object(
            'eligible', 'no',
            'reason', 'Game assignment not found for the given party_id'
        );
    END IF;

    -- Step 2: Get party_type_id from recall_loop.party where id = caregiver_party_id
    -- Step 3: Get party_type_key from recall_loop.party_type where id = party_type_id
    SELECT pt.party_type_key
    INTO party_type_key_val
    FROM recall_loop.party p
    JOIN recall_loop.party_type pt ON p.party_type_id = pt.id
    WHERE p.id = caregiver_party_id;

    IF party_type_key_val IS NULL THEN
        RETURN jsonb_build_object(
            'eligible', 'no',
            'reason', 'Party or party type not found for caregiver_party_id'
        );
    END IF;

    -- Step 4: Check if party_type_key in ('caregiver', 'tenant_admin')
    IF party_type_key_val NOT IN ('caregiver', 'tenant_admin') THEN
        RETURN jsonb_build_object(
            'eligible', 'no',
            'reason', 'Party type not eligible for email notification',
            'party_type', party_type_key_val,
            'caregiver_party_id', caregiver_party_id
        );
    END IF;

    -- Step 5: Get user details for email sending (if eligible)
    SELECT ua.user_id, p.first_name, p.last_name
    INTO user_id_val, user_first_name, user_last_name
    FROM recall_loop.user_account ua
    JOIN recall_loop.person p ON p.party_id = ua.party_id
    WHERE ua.party_id = caregiver_party_id;

    IF user_id_val IS NULL THEN
        RETURN jsonb_build_object(
            'eligible', 'no',
            'reason', 'User account or person details not found for caregiver',
            'party_type', party_type_key_val,
            'caregiver_party_id', caregiver_party_id
        );
    END IF;

    -- Step 6: Get email from auth.users
    SELECT email INTO user_email
    FROM auth.users
    WHERE id = user_id_val;

    IF user_email IS NULL THEN
        RETURN jsonb_build_object(
            'eligible', 'no',
            'reason', 'User email not found in auth.users',
            'party_type', party_type_key_val,
            'caregiver_party_id', caregiver_party_id
        );
    END IF;

    -- Return YES with all user details for email sending
    RETURN jsonb_build_object(
        'eligible', 'yes',
        'caregiver_party_id', caregiver_party_id,
        'party_type', party_type_key_val,
        'user_email', user_email,
        'user_name', CONCAT(user_first_name, ' ', user_last_name),
        'first_name', user_first_name,
        'last_name', user_last_name,
        'user_id', user_id_val,
        'message', 'Party is eligible for email notification'
    );

EXCEPTION
    WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
            exception_message = MESSAGE_TEXT,
            exception_detail = PG_EXCEPTION_DETAIL,
            exception_sqlstate = RETURNED_SQLSTATE,
            exception_hint = PG_EXCEPTION_HINT;

        final_exception_detail := COALESCE(exception_detail, exception_message || ' | SQLSTATE: ' || exception_sqlstate);

        RAISE WARNING 'Error in check_party_eligibility: %, %, %, %',
            exception_message, exception_detail, exception_sqlstate, exception_hint;

        RETURN jsonb_build_object(
            'eligible', 'no',
            'reason', 'Database error occurred while checking party eligibility',
            'error_message', exception_message,
            'error_detail', COALESCE(exception_detail, ''),
            'sqlstate', exception_sqlstate,
            'hint', COALESCE(exception_hint, '')
        );
END;
$function$
;

-- Permissions
ALTER FUNCTION recall_loop.check_party_eligibility(uuid) OWNER TO postgres;
GRANT ALL ON FUNCTION recall_loop.check_party_eligibility(uuid) TO public;
GRANT ALL ON FUNCTION recall_loop.check_party_eligibility(uuid) TO postgres;
GRANT ALL ON FUNCTION recall_loop.check_party_eligibility(uuid) TO anon;
GRANT ALL ON FUNCTION recall_loop.check_party_eligibility(uuid) TO authenticated;
GRANT ALL ON FUNCTION recall_loop.check_party_eligibility(uuid) TO service_role;

-- Test the function (optional - you can remove this section)
-- SELECT recall_loop.check_party_eligibility('your-test-party-id-here'::uuid);
