-- Fix permissions for ms_fn_check_party_email_eligibility RPC function
-- This script ensures the function can access recall_loop schema tables when called from edge functions

-- Grant usage on recall_loop schema to service_role
GRANT USAGE ON SCHEMA recall_loop TO service_role;

-- Grant select permissions on required tables to service_role
GRANT SELECT ON recall_loop.game_assignment TO service_role;
GRANT SELECT ON recall_loop.party TO service_role;
GRANT SELECT ON recall_loop.party_type TO service_role;
GRANT SELECT ON recall_loop.game TO service_role;

-- Ensure the RPC function has proper permissions
GRANT EXECUTE ON FUNCTION public.ms_fn_check_party_email_eligibility(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.ms_fn_check_party_email_eligibility(uuid) TO anon;
GRANT EXECUTE ON FUNCTION public.ms_fn_check_party_email_eligibility(uuid) TO authenticated;

-- Also grant to public for good measure (since it's in public schema)
GRANT EXECUTE ON FUNCTION public.ms_fn_check_party_email_eligibility(uuid) TO public;

-- Set the function to run with definer's rights (as the owner)
-- This ensures it runs with the permissions of the function owner (postgres)
ALTER FUNCTION public.ms_fn_check_party_email_eligibility(uuid) SECURITY DEFINER;

-- Verify permissions (optional - you can comment these out)
-- SELECT has_schema_privilege('service_role', 'recall_loop', 'USAGE');
-- SELECT has_table_privilege('service_role', 'recall_loop.game_assignment', 'SELECT');
-- SELECT has_table_privilege('service_role', 'recall_loop.party', 'SELECT');
-- SELECT has_table_privilege('service_role', 'recall_loop.party_type', 'SELECT');
-- SELECT has_table_privilege('service_role', 'recall_loop.game', 'SELECT');
