import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from "./cors.ts";
import nodemailer from "npm:nodemailer@6.9.10";

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

const transport = nodemailer.createTransport({
  host: Deno.env.get("SMTP_HOST"),
  port: Number(Deno.env.get("SMTP_PORT")),
  secure: true, // Use SSL
  auth: {
    user: Deno.env.get("SMTP_USER"),
    pass: Deno.env.get("SMTP_PASS"),
  },
});



Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method === "POST") {
    try {
      const requestData = await req.json();
      console.log('Request received:', requestData);

      // Extract data from request parameters
      const { game_assignment_id } = requestData;

      if (!game_assignment_id) {
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Missing required field: game_assignment_id"
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Call your RPC function to check party eligibility
      console.log('Calling RPC with game_assignment_id:', game_assignment_id);
      const { data: eligibilityResult, error: rpcError } = await supabase
        .rpc('ms_fn_check_party_email_eligibility', { game_assignment_id: game_assignment_id });

      console.log('RPC raw response - data:', eligibilityResult);
      console.log('RPC raw response - error:', rpcError);

      if (rpcError) {
        console.error('RPC Error:', rpcError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Database error",
            error: rpcError.message
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      console.log('Eligibility check result:', eligibilityResult);
      console.log('Eligibility check result type:', typeof eligibilityResult);
      console.log('Eligibility check result JSON:', JSON.stringify(eligibilityResult, null, 2));
      console.log('Eligible value:', eligibilityResult?.eligible);
      console.log('Eligible value type:', typeof eligibilityResult?.eligible);
      console.log('Is eligibilityResult falsy?', !eligibilityResult);
      console.log('Is eligible === no?', eligibilityResult?.eligible === 'no');

      // Handle Supabase RPC response format
      let actualResult = eligibilityResult;

      // Supabase RPC returns: [{ "function_name": { actual_data } }]
      if (Array.isArray(eligibilityResult) && eligibilityResult.length > 0) {
        const firstElement = eligibilityResult[0];
        if (firstElement && firstElement.ms_fn_check_party_email_eligibility) {
          actualResult = firstElement.ms_fn_check_party_email_eligibility;
          console.log('Extracted from RPC wrapper:', actualResult);
        } else {
          actualResult = firstElement;
          console.log('Using first array element:', actualResult);
        }
      }

      console.log('Final actualResult:', JSON.stringify(actualResult, null, 2));
      console.log('Final eligible value:', actualResult?.eligible);

      // Check if the response is "no" - not eligible for email
      if (!actualResult || actualResult?.eligible === 'no') {
        console.log(`Party not eligible: ${actualResult?.message || actualResult?.reason || 'Not eligible'}`);
        return new Response(
          JSON.stringify({
            status: "success",
            message: "Party not eligible for email notification",
            reason: actualResult?.message || actualResult?.reason || 'Not eligible',
            party_type: actualResult?.party_type || null
          }),
          {
            status: 200,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Check if eligible is "yes"
      if (actualResult?.eligible !== 'yes') {
        console.log(`Unexpected eligibility status: ${actualResult?.eligible}`);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Unexpected eligibility status",
            eligibility_result: actualResult
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // If we reach here, actualResult.eligible === 'yes'
      // Get user details directly from auth.users using the party_id as user_id
      const party_id = actualResult.party_id;
      const { data: authUser, error: authUserError } = await supabase.auth.admin
        .getUserById(party_id);

      if (authUserError || !authUser.user) {
        console.error('Error fetching auth user:', authUserError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "User not found in auth"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Prepare email data using auth user data
      const userName = authUser.user.user_metadata?.full_name || authUser.user.email?.split('@')[0] || 'User';
      const recipientEmail = authUser.user.email;
      const partyType = actualResult.party_type;
      const gameName = actualResult.game_name;
      const assignedOn = new Date(actualResult.assigned_on).toLocaleDateString();

      // Game assignment email content
      const emailContent = `
<p>Dear ${userName},</p>

<p>Great news! You've been assigned a new game on the Competitor LDC Coaching Centre platform.</p>

<p>
  <strong>Game:</strong> ${gameName}<br>
  <strong>Assigned On:</strong> ${assignedOn}<br>
  <strong>Your Role:</strong> ${partyType}
</p>

<p>
  Please log in to your account to start playing and continue your progress.
</p>

<br>
<p>Best regards,<br>
Competitor LDC Coaching Centre Team</p>
      `;

      // Send email
      await new Promise<void>((resolve, reject) => {
        transport.sendMail({
          from: Deno.env.get("SMTP_USER"),
          to: recipientEmail,
          subject: "New Game Assignment - Competitor LDC Coaching Centre",
          html: emailContent,
        }, (error: any) => {
          if (error) {
            return reject(error);
          }
          resolve();
        });
      });

      console.log(`Email sent successfully to ${recipientEmail} for game: ${gameName}`);

      return new Response(
        JSON.stringify({
          status: "success",
          message: "Game assignment email sent successfully",
          eligible: "yes",
          details: {
            recipient: recipientEmail,
            userName: userName,
            partyType: partyType,
            gameName: gameName,
            assignedOn: assignedOn,
            party_id: party_id,
            game_assignment_id: game_assignment_id
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );

    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response(
        JSON.stringify({
          status: "error",
          message: "Internal server error",
          error: error.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
  } else {
    return new Response("Method not allowed", { status: 405 });
  }
});
