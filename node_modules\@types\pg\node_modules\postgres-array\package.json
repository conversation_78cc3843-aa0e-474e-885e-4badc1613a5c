{"name": "postgres-array", "main": "index.js", "version": "3.0.2", "description": "Parse postgres array columns", "license": "MIT", "repository": "bendrucker/postgres-array", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "engines": {"node": ">=12"}, "scripts": {"test": "standard && tape test.js"}, "types": "index.d.ts", "keywords": ["postgres", "array", "parser"], "dependencies": {}, "devDependencies": {"standard": "^17.0.0", "tape": "^5.0.0"}, "files": ["index.js", "index.d.ts", "readme.md"]}