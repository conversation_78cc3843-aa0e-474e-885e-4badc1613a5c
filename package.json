{"name": "employee-notification", "version": "1.0.0", "description": "RecallLoop employee notification system", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["employee", "notification", "recall", "supabase"], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.4.5", "express": "^4.21.1", "nodemailer": "^6.9.15", "pg": "^8.13.0"}, "devDependencies": {"@types/node": "^22.7.5", "@types/nodemailer": "^6.4.16", "@types/pg": "^8.11.10"}}