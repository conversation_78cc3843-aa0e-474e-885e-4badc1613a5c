{"name": "@types/pg", "version": "8.11.10", "description": "TypeScript definitions for pg", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pg", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "pspeter3", "url": "https://github.com/pspeter3"}, {"name": "<PERSON>", "githubUsername": "HoldYourWaffle", "url": "https://github.com/HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pg"}, "scripts": {}, "dependencies": {"@types/node": "*", "pg-protocol": "*", "pg-types": "^4.0.1"}, "typesPublisherContentHash": "f9223c64d2d5c66dfe34da7582116e7ba7c44142c47680880743559b0dde84bc", "typeScriptVersion": "4.8"}